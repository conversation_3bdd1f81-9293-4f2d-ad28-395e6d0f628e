<?php
/**
 * Comprehensive test for diagnostic auto-fix module interface issues
 * Tests: Recent Fixes List, Progress Tracking, and Error Investigation
 */

// WordPress environment
define('WP_USE_THEMES', false);
require_once('../../../wp-load.php');

if (!current_user_can('manage_options')) {
    die('Access denied');
}

echo "<h1>🔧 Diagnostic Interface Issues Investigation</h1>\n";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .test-section { border: 1px solid #ddd; margin: 20px 0; padding: 15px; }
    .success { color: green; }
    .error { color: red; }
    .warning { color: orange; }
    pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
</style>\n";

// =============================================================================
// ISSUE 1: RECENT FIXES LIST INVESTIGATION
// =============================================================================

echo "<div class='test-section'>\n";
echo "<h2>🔍 ISSUE 1: Recent Fixes List Investigation</h2>\n";

echo "<h3>Step 1: Check Fix History Storage</h3>\n";
$fix_history = get_option('redco_diagnostic_fix_history', array());
echo "Fix history count: " . count($fix_history) . "\n";

if (!empty($fix_history)) {
    echo "<div class='success'>✅ Fix history exists</div>\n";
    echo "<h4>Recent Fix Sessions:</h4>\n";
    $recent_sessions = array_slice(array_reverse($fix_history), 0, 3);
    foreach ($recent_sessions as $index => $session) {
        $timestamp = isset($session['timestamp']) ? date('Y-m-d H:i:s', $session['timestamp']) : 'Unknown';
        $fixes_applied = isset($session['fixes_applied']) ? $session['fixes_applied'] : 0;
        echo "Session " . ($index + 1) . ": {$fixes_applied} fixes applied at {$timestamp}<br>\n";
    }
} else {
    echo "<div class='warning'>⚠️ No fix history found - this explains empty Recent Fixes list</div>\n";
}

echo "<h3>Step 2: Test Recent Fixes AJAX Endpoint</h3>\n";
if (class_exists('Redco_Diagnostic_AutoFix')) {
    $diagnostic = new Redco_Diagnostic_AutoFix();
    
    // Simulate AJAX request
    $_POST['nonce'] = wp_create_nonce('redco_diagnostic_nonce');
    
    ob_start();
    try {
        $diagnostic->ajax_load_recent_fixes();
        $ajax_output = ob_get_clean();
        
        if (!empty($ajax_output)) {
            echo "<div class='success'>✅ AJAX endpoint responds</div>\n";
            echo "<h4>AJAX Response:</h4>\n";
            echo "<pre>" . htmlspecialchars($ajax_output) . "</pre>\n";
        } else {
            echo "<div class='error'>❌ AJAX endpoint returned no data</div>\n";
        }
    } catch (Exception $e) {
        ob_end_clean();
        echo "<div class='error'>❌ AJAX endpoint error: " . $e->getMessage() . "</div>\n";
    }
} else {
    echo "<div class='error'>❌ Redco_Diagnostic_AutoFix class not found</div>\n";
}

echo "<h3>Step 3: Create Test Fix Session</h3>\n";
$test_session = array(
    'timestamp' => time(),
    'fixes_applied' => 2,
    'fixes_failed' => 0,
    'backup_created' => true,
    'rollback_id' => 'test_' . time(),
    'details' => array(
        array(
            'issue_id' => 'test_security_header',
            'fix_type' => 'security_header',
            'status' => 'success',
            'message' => 'Added X-Frame-Options header'
        ),
        array(
            'issue_id' => 'test_gzip_compression',
            'fix_type' => 'compression',
            'status' => 'success',
            'message' => 'Enabled GZIP compression'
        )
    )
);

$fix_history[] = $test_session;
update_option('redco_diagnostic_fix_history', $fix_history);
echo "<div class='success'>✅ Test fix session created</div>\n";

echo "</div>\n";

// =============================================================================
// ISSUE 2: PROGRESS TRACKING INVESTIGATION
// =============================================================================

echo "<div class='test-section'>\n";
echo "<h2>🔍 ISSUE 2: Progress Tracking Investigation</h2>\n";

echo "<h3>Step 1: Check JavaScript Variables</h3>\n";
echo "<script>
console.log('=== DIAGNOSTIC INTERFACE DEBUG ===');
console.log('redcoDiagnosticAjax object:', typeof redcoDiagnosticAjax !== 'undefined' ? redcoDiagnosticAjax : 'UNDEFINED');
console.log('jQuery loaded:', typeof jQuery !== 'undefined');
console.log('Diagnostic object:', typeof RedcoDiagnostic !== 'undefined' ? 'DEFINED' : 'UNDEFINED');

// Test progress update function
if (typeof RedcoDiagnostic !== 'undefined' && RedcoDiagnostic.updateProgress) {
    console.log('✅ updateProgress function exists');
    // Test with valid data
    try {
        RedcoDiagnostic.updateProgress(50, 'Test progress message');
        console.log('✅ updateProgress function works with valid data');
    } catch (e) {
        console.error('❌ updateProgress function error:', e);
    }
    
    // Test with undefined percentage
    try {
        RedcoDiagnostic.updateProgress(undefined, 'Test with undefined percentage');
        console.log('⚠️ updateProgress accepts undefined percentage');
    } catch (e) {
        console.error('❌ updateProgress error with undefined:', e);
    }
} else {
    console.error('❌ updateProgress function not found');
}

// Check for progress modal elements
setTimeout(function() {
    const progressFill = document.querySelector('.progress-fill');
    const progressText = document.querySelector('.progress-text');
    console.log('Progress fill element:', progressFill ? 'FOUND' : 'NOT FOUND');
    console.log('Progress text element:', progressText ? 'FOUND' : 'NOT FOUND');
}, 1000);
</script>\n";

echo "<h3>Step 2: Simulate Progress Update Issues</h3>\n";
echo "<p>Check browser console for JavaScript debug information about progress tracking.</p>\n";

echo "</div>\n";

// =============================================================================
// ISSUE 3: ERROR INVESTIGATION
// =============================================================================

echo "<div class='test-section'>\n";
echo "<h2>🔍 ISSUE 3: Comprehensive Error Investigation</h2>\n";

echo "<h3>Step 1: Check WordPress Error Log</h3>\n";
$error_log_path = ini_get('error_log');
if ($error_log_path && file_exists($error_log_path)) {
    echo "<div class='success'>✅ Error log found: {$error_log_path}</div>\n";
    
    // Read last 50 lines of error log
    $error_lines = array();
    $handle = fopen($error_log_path, 'r');
    if ($handle) {
        $lines = array();
        while (($line = fgets($handle)) !== false) {
            $lines[] = $line;
            if (count($lines) > 100) {
                array_shift($lines); // Keep only last 100 lines
            }
        }
        fclose($handle);
        
        // Filter for diagnostic-related errors
        $diagnostic_errors = array_filter($lines, function($line) {
            return stripos($line, 'redco') !== false || 
                   stripos($line, 'diagnostic') !== false ||
                   stripos($line, 'autofix') !== false;
        });
        
        if (!empty($diagnostic_errors)) {
            echo "<h4>Recent Diagnostic-Related Errors:</h4>\n";
            echo "<pre>" . htmlspecialchars(implode('', array_slice($diagnostic_errors, -10))) . "</pre>\n";
        } else {
            echo "<div class='success'>✅ No recent diagnostic-related errors found</div>\n";
        }
    }
} else {
    echo "<div class='warning'>⚠️ Error log not accessible or not configured</div>\n";
}

echo "<h3>Step 2: Test AJAX Endpoints</h3>\n";

// Test diagnostic scan endpoint
echo "<h4>Testing Diagnostic Scan Endpoint:</h4>\n";
$scan_data = array(
    'action' => 'redco_run_diagnostic_scan',
    'nonce' => wp_create_nonce('redco_diagnostic_nonce'),
    'scan_type' => 'quick'
);

$_POST = $scan_data;
if (class_exists('Redco_Diagnostic_AutoFix')) {
    $diagnostic = new Redco_Diagnostic_AutoFix();
    ob_start();
    try {
        $diagnostic->ajax_run_diagnostic_scan();
        $scan_output = ob_get_clean();
        echo "<div class='success'>✅ Scan endpoint responds</div>\n";
        
        // Parse JSON response
        $response = json_decode($scan_output, true);
        if ($response) {
            echo "Response type: " . (isset($response['success']) ? ($response['success'] ? 'SUCCESS' : 'ERROR') : 'UNKNOWN') . "<br>\n";
            if (isset($response['data'])) {
                echo "Data keys: " . implode(', ', array_keys($response['data'])) . "<br>\n";
            }
        }
    } catch (Exception $e) {
        ob_end_clean();
        echo "<div class='error'>❌ Scan endpoint error: " . $e->getMessage() . "</div>\n";
    }
}

echo "<h3>Step 3: Check Asset Loading</h3>\n";
echo "<script>
// Check if diagnostic assets are loaded
const diagnosticCSS = document.querySelector('link[href*=\"diagnostic-autofix.css\"]');
const diagnosticJS = document.querySelector('script[src*=\"diagnostic-autofix.js\"]');

console.log('Diagnostic CSS loaded:', diagnosticCSS ? 'YES' : 'NO');
console.log('Diagnostic JS loaded:', diagnosticJS ? 'YES' : 'NO');

// Check for common JavaScript errors
window.addEventListener('error', function(e) {
    if (e.filename && e.filename.includes('diagnostic')) {
        console.error('🚨 DIAGNOSTIC JS ERROR:', {
            message: e.message,
            filename: e.filename,
            lineno: e.lineno,
            colno: e.colno
        });
    }
});
</script>\n";

echo "</div>\n";

echo "<h2>🎯 Investigation Summary</h2>\n";
echo "<p>Check the browser console for JavaScript debug information and any error messages.</p>\n";
echo "<p>The investigation above should reveal the root causes of the three interface issues.</p>\n";
?>
