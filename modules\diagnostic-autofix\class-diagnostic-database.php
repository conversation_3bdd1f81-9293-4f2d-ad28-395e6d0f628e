<?php
/**
 * Memory-Safe Database Operations for Diagnostic Module
 * 
 * Provides chunked database operations to prevent memory exhaustion
 */

if (!defined('ABSPATH')) {
    exit;
}

class Redco_Diagnostic_Database {
    
    /**
     * Default chunk size for large operations
     */
    const DEFAULT_CHUNK_SIZE = 1000;
    
    /**
     * Memory threshold (80% of limit)
     */
    private $memory_threshold;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->memory_threshold = $this->get_memory_limit() * 0.8;
    }
    
    /**
     * Get autoload size with memory management
     */
    public function get_autoload_size() {
        global $wpdb;
        
        try {
            $result = $wpdb->get_var("
                SELECT SUM(LENGTH(option_value))
                FROM {$wpdb->options}
                WHERE autoload = 'yes'
            ");
            
            return $result ? (int) $result : 0;
            
        } catch (Exception $e) {
            Redco_Diagnostic_Error_Handler::handle_exception($e, Redco_Diagnostic_Error_Handler::CONTEXT_SYSTEM);
            return 0;
        }
    }
    
    /**
     * Get large autoload options with chunked processing
     */
    public function get_large_autoload_options($size_threshold = 50000, $limit = 10) {
        global $wpdb;
        
        try {
            $results = $wpdb->get_results($wpdb->prepare("
                SELECT option_name, LENGTH(option_value) as size
                FROM {$wpdb->options}
                WHERE autoload = 'yes'
                AND LENGTH(option_value) > %d
                ORDER BY size DESC
                LIMIT %d
            ", $size_threshold, $limit));
            
            return $results ?: array();
            
        } catch (Exception $e) {
            Redco_Diagnostic_Error_Handler::handle_exception($e, Redco_Diagnostic_Error_Handler::CONTEXT_SYSTEM);
            return array();
        }
    }
    
    /**
     * Get database size
     */
    public function get_database_size() {
        global $wpdb;
        
        try {
            $result = $wpdb->get_var($wpdb->prepare("
                SELECT SUM(data_length + index_length)
                FROM information_schema.tables
                WHERE table_schema = %s
            ", $wpdb->dbname));
            
            return $result ? (int) $result : 0;
            
        } catch (Exception $e) {
            Redco_Diagnostic_Error_Handler::handle_exception($e, Redco_Diagnostic_Error_Handler::CONTEXT_SYSTEM);
            return 0;
        }
    }
    
    /**
     * Clean up database with chunked processing
     */
    public function cleanup_database($operations = array()) {
        $results = array(
            'operations_completed' => 0,
            'operations_failed' => 0,
            'details' => array()
        );
        
        $default_operations = array(
            'cleanup_transients',
            'cleanup_post_revisions',
            'cleanup_spam_comments',
            'cleanup_trash_posts',
            'optimize_tables'
        );
        
        $operations = !empty($operations) ? $operations : $default_operations;
        
        foreach ($operations as $operation) {
            try {
                $this->check_memory_usage();
                
                $operation_result = $this->execute_cleanup_operation($operation);
                
                if ($operation_result['success']) {
                    $results['operations_completed']++;
                } else {
                    $results['operations_failed']++;
                }
                
                $results['details'][$operation] = $operation_result;
                
            } catch (Exception $e) {
                $results['operations_failed']++;
                $results['details'][$operation] = array(
                    'success' => false,
                    'message' => 'Exception: ' . $e->getMessage()
                );
                
                Redco_Diagnostic_Error_Handler::handle_exception($e, Redco_Diagnostic_Error_Handler::CONTEXT_SYSTEM);
            }
        }
        
        return $results;
    }
    
    /**
     * Execute individual cleanup operation
     */
    private function execute_cleanup_operation($operation) {
        switch ($operation) {
            case 'cleanup_transients':
                return $this->cleanup_expired_transients();
            case 'cleanup_post_revisions':
                return $this->cleanup_post_revisions();
            case 'cleanup_spam_comments':
                return $this->cleanup_spam_comments();
            case 'cleanup_trash_posts':
                return $this->cleanup_trash_posts();
            case 'optimize_tables':
                return $this->optimize_database_tables();
            default:
                return array(
                    'success' => false,
                    'message' => 'Unknown operation: ' . $operation
                );
        }
    }
    
    /**
     * Clean up expired transients with chunked processing
     */
    private function cleanup_expired_transients() {
        global $wpdb;
        
        try {
            $current_time = time();
            $chunk_size = self::DEFAULT_CHUNK_SIZE;
            $total_deleted = 0;
            
            do {
                $this->check_memory_usage();
                
                // Get expired transients in chunks
                $expired_transients = $wpdb->get_col($wpdb->prepare("
                    SELECT option_name
                    FROM {$wpdb->options}
                    WHERE option_name LIKE '_transient_timeout_%'
                    AND option_value < %d
                    LIMIT %d
                ", $current_time, $chunk_size));
                
                if (empty($expired_transients)) {
                    break;
                }
                
                // Delete expired transients and their values
                foreach ($expired_transients as $timeout_option) {
                    $transient_name = str_replace('_transient_timeout_', '', $timeout_option);
                    $value_option = '_transient_' . $transient_name;
                    
                    $wpdb->delete($wpdb->options, array('option_name' => $timeout_option));
                    $wpdb->delete($wpdb->options, array('option_name' => $value_option));
                    
                    $total_deleted += 2;
                }
                
                // Force garbage collection
                if (function_exists('gc_collect_cycles')) {
                    gc_collect_cycles();
                }
                
            } while (count($expired_transients) === $chunk_size);
            
            return array(
                'success' => true,
                'message' => "Cleaned up {$total_deleted} expired transient options",
                'items_deleted' => $total_deleted
            );
            
        } catch (Exception $e) {
            return array(
                'success' => false,
                'message' => 'Failed to cleanup transients: ' . $e->getMessage()
            );
        }
    }
    
    /**
     * Clean up post revisions with chunked processing
     */
    private function cleanup_post_revisions() {
        global $wpdb;
        
        try {
            $chunk_size = self::DEFAULT_CHUNK_SIZE;
            $total_deleted = 0;
            
            do {
                $this->check_memory_usage();
                
                // Get post revisions in chunks
                $revision_ids = $wpdb->get_col($wpdb->prepare("
                    SELECT ID
                    FROM {$wpdb->posts}
                    WHERE post_type = 'revision'
                    LIMIT %d
                ", $chunk_size));
                
                if (empty($revision_ids)) {
                    break;
                }
                
                // Delete revisions and their meta
                $ids_placeholder = implode(',', array_fill(0, count($revision_ids), '%d'));
                
                $wpdb->query($wpdb->prepare("
                    DELETE FROM {$wpdb->postmeta}
                    WHERE post_id IN ({$ids_placeholder})
                ", $revision_ids));
                
                $wpdb->query($wpdb->prepare("
                    DELETE FROM {$wpdb->posts}
                    WHERE ID IN ({$ids_placeholder})
                ", $revision_ids));
                
                $total_deleted += count($revision_ids);
                
                // Force garbage collection
                if (function_exists('gc_collect_cycles')) {
                    gc_collect_cycles();
                }
                
            } while (count($revision_ids) === $chunk_size);
            
            return array(
                'success' => true,
                'message' => "Cleaned up {$total_deleted} post revisions",
                'items_deleted' => $total_deleted
            );
            
        } catch (Exception $e) {
            return array(
                'success' => false,
                'message' => 'Failed to cleanup post revisions: ' . $e->getMessage()
            );
        }
    }
    
    /**
     * Clean up spam comments with chunked processing
     */
    private function cleanup_spam_comments() {
        global $wpdb;
        
        try {
            $chunk_size = self::DEFAULT_CHUNK_SIZE;
            $total_deleted = 0;
            
            do {
                $this->check_memory_usage();
                
                // Get spam comments in chunks
                $spam_ids = $wpdb->get_col($wpdb->prepare("
                    SELECT comment_ID
                    FROM {$wpdb->comments}
                    WHERE comment_approved = 'spam'
                    LIMIT %d
                ", $chunk_size));
                
                if (empty($spam_ids)) {
                    break;
                }
                
                // Delete spam comments and their meta
                $ids_placeholder = implode(',', array_fill(0, count($spam_ids), '%d'));
                
                $wpdb->query($wpdb->prepare("
                    DELETE FROM {$wpdb->commentmeta}
                    WHERE comment_id IN ({$ids_placeholder})
                ", $spam_ids));
                
                $wpdb->query($wpdb->prepare("
                    DELETE FROM {$wpdb->comments}
                    WHERE comment_ID IN ({$ids_placeholder})
                ", $spam_ids));
                
                $total_deleted += count($spam_ids);
                
                // Force garbage collection
                if (function_exists('gc_collect_cycles')) {
                    gc_collect_cycles();
                }
                
            } while (count($spam_ids) === $chunk_size);
            
            return array(
                'success' => true,
                'message' => "Cleaned up {$total_deleted} spam comments",
                'items_deleted' => $total_deleted
            );
            
        } catch (Exception $e) {
            return array(
                'success' => false,
                'message' => 'Failed to cleanup spam comments: ' . $e->getMessage()
            );
        }
    }
    
    /**
     * Clean up trash posts
     */
    private function cleanup_trash_posts() {
        // Implementation similar to other cleanup methods
        return array(
            'success' => true,
            'message' => 'Trash posts cleanup not yet implemented',
            'items_deleted' => 0
        );
    }
    
    /**
     * Optimize database tables
     */
    private function optimize_database_tables() {
        global $wpdb;
        
        try {
            $tables = $wpdb->get_col("SHOW TABLES");
            $optimized_count = 0;
            
            foreach ($tables as $table) {
                $this->check_memory_usage();
                
                $result = $wpdb->query("OPTIMIZE TABLE `{$table}`");
                if ($result !== false) {
                    $optimized_count++;
                }
            }
            
            return array(
                'success' => true,
                'message' => "Optimized {$optimized_count} database tables",
                'tables_optimized' => $optimized_count
            );
            
        } catch (Exception $e) {
            return array(
                'success' => false,
                'message' => 'Failed to optimize tables: ' . $e->getMessage()
            );
        }
    }
    
    /**
     * Check memory usage and throw exception if threshold exceeded
     */
    private function check_memory_usage() {
        $current_memory = memory_get_usage(true);
        
        if ($current_memory > $this->memory_threshold) {
            if (function_exists('gc_collect_cycles')) {
                gc_collect_cycles();
            }
            
            // Check again after garbage collection
            if (memory_get_usage(true) > $this->memory_threshold) {
                throw new Exception('Memory usage exceeded safe threshold');
            }
        }
    }
    
    /**
     * Get memory limit in bytes
     */
    private function get_memory_limit() {
        $memory_limit = ini_get('memory_limit');
        
        if (is_numeric($memory_limit)) {
            return (int) $memory_limit;
        }
        
        $unit = strtoupper(substr($memory_limit, -1));
        $value = (int) substr($memory_limit, 0, -1);
        
        switch ($unit) {
            case 'G':
                return $value * 1024 * 1024 * 1024;
            case 'M':
                return $value * 1024 * 1024;
            case 'K':
                return $value * 1024;
            default:
                return $value;
        }
    }
}
