<?php
/**
 * Diagnostic Scanner for Redco Optimizer
 * 
 * Handles all diagnostic scanning operations
 * Separated from main class for better maintainability
 */

if (!defined('ABSPATH')) {
    exit;
}

class Redco_Diagnostic_Scanner {
    
    /**
     * Scanner settings
     */
    private $settings = array();
    
    /**
     * Memory management settings
     */
    private $memory_limit_bytes;
    private $max_execution_time;
    
    /**
     * Constructor
     */
    public function __construct($settings = array()) {
        $this->settings = wp_parse_args($settings, array(
            'memory_limit' => '512M',
            'execution_time' => 300,
            'chunk_size' => 1000,
            'enable_progress_tracking' => true
        ));
        
        $this->memory_limit_bytes = $this->parse_memory_limit($this->settings['memory_limit']);
        $this->max_execution_time = $this->settings['execution_time'];
    }
    
    /**
     * Run comprehensive diagnostic scan with memory management
     */
    public function run_scan($scan_type = 'comprehensive', $include_pagespeed = false) {
        // Set execution limits
        $original_time_limit = ini_get('max_execution_time');
        $original_memory_limit = ini_get('memory_limit');
        
        @ini_set('max_execution_time', $this->max_execution_time);
        @ini_set('memory_limit', $this->settings['memory_limit']);
        
        try {
            $results = $this->execute_scan($scan_type, $include_pagespeed);
            return $results;
        } catch (Exception $e) {
            error_log('Redco Scanner Error: ' . $e->getMessage());
            throw $e;
        } finally {
            // Always restore original limits
            @ini_set('max_execution_time', $original_time_limit);
            @ini_set('memory_limit', $original_memory_limit);
        }
    }
    
    /**
     * Execute the actual scan with memory monitoring
     */
    private function execute_scan($scan_type, $include_pagespeed) {
        $start_time = microtime(true);
        $initial_memory = memory_get_usage(true);
        
        $results = array(
            'scan_type' => $scan_type,
            'timestamp' => time(),
            'issues' => array(),
            'optimization_opportunities' => array(),
            'performance_score' => 0,
            'health_score' => 0,
            'scan_duration' => 0,
            'memory_usage' => array(
                'initial' => $initial_memory,
                'peak' => 0
            )
        );
        
        // Clear previous results
        delete_option('redco_diagnostic_results');
        delete_option('redco_diagnostic_stats');
        
        // Define scan steps
        $scan_steps = $this->get_scan_steps($scan_type);
        $total_steps = count($scan_steps) + ($include_pagespeed ? 1 : 0);
        $current_step = 0;
        
        // Execute each scan step with memory monitoring
        foreach ($scan_steps as $step) {
            $current_step++;
            
            if ($this->settings['enable_progress_tracking']) {
                $this->update_scan_progress($current_step, $total_steps, "Scanning {$step}...");
            }
            
            // Check memory usage before each step
            $this->check_memory_usage();
            
            $step_results = $this->execute_scan_step($step);
            $results['issues'] = array_merge($results['issues'], $step_results);
            
            // Force garbage collection after each step
            $this->cleanup_memory();
        }
        
        // PageSpeed scan if requested
        if ($include_pagespeed && !empty($this->settings['pagespeed_api_key'])) {
            $current_step++;
            if ($this->settings['enable_progress_tracking']) {
                $this->update_scan_progress($current_step, $total_steps, 'Running PageSpeed analysis...');
            }
            
            $pagespeed_results = $this->scan_pagespeed_issues();
            $results['issues'] = array_merge($results['issues'], $pagespeed_results['issues']);
            $results['pagespeed_score'] = $pagespeed_results['score'];
        }
        
        // Calculate final metrics
        $results['scan_duration'] = round((microtime(true) - $start_time) * 1000);
        $results['memory_usage']['peak'] = memory_get_peak_usage(true);
        $results['health_score'] = $this->calculate_health_score($results['issues']);
        $results['performance_score'] = $this->calculate_performance_score($results['issues']);
        
        // Store results
        update_option('redco_diagnostic_results', $results);
        
        return $results;
    }
    
    /**
     * Get scan steps based on scan type
     */
    private function get_scan_steps($scan_type) {
        switch ($scan_type) {
            case 'comprehensive':
                return array('wordpress', 'database', 'frontend', 'server', 'security', 'modules');
            case 'quick':
                return array('wordpress', 'frontend');
            default:
                return array($scan_type);
        }
    }
    
    /**
     * Execute individual scan step
     */
    private function execute_scan_step($step) {
        switch ($step) {
            case 'wordpress':
                return $this->scan_wordpress_issues();
            case 'database':
                return $this->scan_database_issues();
            case 'frontend':
                return $this->scan_frontend_issues();
            case 'server':
                return $this->scan_server_issues();
            case 'security':
                return $this->scan_security_issues();
            case 'modules':
                return $this->scan_module_issues();
            default:
                return array();
        }
    }
    
    /**
     * Memory management methods
     */
    private function check_memory_usage() {
        $current_memory = memory_get_usage(true);
        $memory_threshold = $this->memory_limit_bytes * 0.8; // 80% threshold
        
        if ($current_memory > $memory_threshold) {
            $this->cleanup_memory();
            
            // If still over threshold, throw exception
            if (memory_get_usage(true) > $memory_threshold) {
                throw new Exception('Memory usage exceeded safe threshold');
            }
        }
    }
    
    /**
     * Force garbage collection and cleanup
     */
    private function cleanup_memory() {
        if (function_exists('gc_collect_cycles')) {
            gc_collect_cycles();
        }
        
        // Clear any large temporary variables
        global $wpdb;
        if (isset($wpdb->queries)) {
            $wpdb->queries = array();
        }
    }
    
    /**
     * Parse memory limit string to bytes
     */
    private function parse_memory_limit($memory_limit) {
        if (is_numeric($memory_limit)) {
            return (int) $memory_limit;
        }
        
        $unit = strtoupper(substr($memory_limit, -1));
        $value = (int) substr($memory_limit, 0, -1);
        
        switch ($unit) {
            case 'G':
                return $value * 1024 * 1024 * 1024;
            case 'M':
                return $value * 1024 * 1024;
            case 'K':
                return $value * 1024;
            default:
                return $value;
        }
    }
    
    /**
     * Update scan progress
     */
    private function update_scan_progress($current_step, $total_steps, $message) {
        $progress = array(
            'current_step' => $current_step,
            'total_steps' => $total_steps,
            'percentage' => round(($current_step / $total_steps) * 100),
            'message' => $message,
            'timestamp' => time()
        );
        
        set_transient('redco_scan_progress', $progress, 300);
        do_action('redco_scan_progress_update', $progress);
    }
    
    /**
     * Placeholder methods for actual scanning logic
     * These will be implemented in the next phase
     */
    private function scan_wordpress_issues() {
        // Implementation will be moved from main class
        return array();
    }
    
    private function scan_database_issues() {
        // Implementation will be moved from main class
        return array();
    }
    
    private function scan_frontend_issues() {
        // Implementation will be moved from main class
        return array();
    }
    
    private function scan_server_issues() {
        // Implementation will be moved from main class
        return array();
    }
    
    private function scan_security_issues() {
        // Implementation will be moved from main class
        return array();
    }
    
    private function scan_module_issues() {
        // Implementation will be moved from main class
        return array();
    }
    
    private function scan_pagespeed_issues() {
        // Implementation will be moved from main class
        return array('issues' => array(), 'score' => 0);
    }
    
    private function calculate_health_score($issues) {
        // Implementation will be moved from main class
        return 100;
    }
    
    private function calculate_performance_score($issues) {
        // Implementation will be moved from main class
        return 100;
    }
}
