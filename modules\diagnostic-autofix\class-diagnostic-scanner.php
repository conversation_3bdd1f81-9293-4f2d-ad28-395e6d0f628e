<?php
/**
 * Diagnostic Scanner for Redco Optimizer
 * 
 * Handles all diagnostic scanning operations
 * Separated from main class for better maintainability
 */

if (!defined('ABSPATH')) {
    exit;
}

class Redco_Diagnostic_Scanner {
    
    /**
     * Scanner settings
     */
    private $settings = array();
    
    /**
     * Memory management settings
     */
    private $memory_limit_bytes;
    private $max_execution_time;
    
    /**
     * Constructor
     */
    public function __construct($settings = array()) {
        $this->settings = wp_parse_args($settings, array(
            'memory_limit' => '512M',
            'execution_time' => 300,
            'chunk_size' => 1000,
            'enable_progress_tracking' => true
        ));
        
        $this->memory_limit_bytes = $this->parse_memory_limit($this->settings['memory_limit']);
        $this->max_execution_time = $this->settings['execution_time'];
    }
    
    /**
     * Run comprehensive diagnostic scan with memory management
     */
    public function run_scan($scan_type = 'comprehensive', $include_pagespeed = false) {
        // Set execution limits
        $original_time_limit = ini_get('max_execution_time');
        $original_memory_limit = ini_get('memory_limit');
        
        @ini_set('max_execution_time', $this->max_execution_time);
        @ini_set('memory_limit', $this->settings['memory_limit']);
        
        try {
            $results = $this->execute_scan($scan_type, $include_pagespeed);
            return $results;
        } catch (Exception $e) {
            error_log('Redco Scanner Error: ' . $e->getMessage());
            throw $e;
        } finally {
            // Always restore original limits
            @ini_set('max_execution_time', $original_time_limit);
            @ini_set('memory_limit', $original_memory_limit);
        }
    }
    
    /**
     * Execute the actual scan with memory monitoring
     */
    private function execute_scan($scan_type, $include_pagespeed) {
        $start_time = microtime(true);
        $initial_memory = memory_get_usage(true);
        
        $results = array(
            'scan_type' => $scan_type,
            'timestamp' => time(),
            'issues' => array(),
            'optimization_opportunities' => array(),
            'performance_score' => 0,
            'health_score' => 0,
            'scan_duration' => 0,
            'memory_usage' => array(
                'initial' => $initial_memory,
                'peak' => 0
            )
        );
        
        // Clear previous results
        delete_option('redco_diagnostic_results');
        delete_option('redco_diagnostic_stats');
        
        // Define scan steps
        $scan_steps = $this->get_scan_steps($scan_type);
        $total_steps = count($scan_steps) + ($include_pagespeed ? 1 : 0);
        $current_step = 0;
        
        // Execute each scan step with memory monitoring
        foreach ($scan_steps as $step) {
            $current_step++;
            
            if ($this->settings['enable_progress_tracking']) {
                $this->update_scan_progress($current_step, $total_steps, "Scanning {$step}...");
            }
            
            // Check memory usage before each step
            $this->check_memory_usage();
            
            $step_results = $this->execute_scan_step($step);
            $results['issues'] = array_merge($results['issues'], $step_results);
            
            // Force garbage collection after each step
            $this->cleanup_memory();
        }
        
        // PageSpeed scan if requested
        if ($include_pagespeed && !empty($this->settings['pagespeed_api_key'])) {
            $current_step++;
            if ($this->settings['enable_progress_tracking']) {
                $this->update_scan_progress($current_step, $total_steps, 'Running PageSpeed analysis...');
            }
            
            $pagespeed_results = $this->scan_pagespeed_issues();
            $results['issues'] = array_merge($results['issues'], $pagespeed_results['issues']);
            $results['pagespeed_score'] = $pagespeed_results['score'];
        }
        
        // Calculate final metrics
        $results['scan_duration'] = round((microtime(true) - $start_time) * 1000);
        $results['memory_usage']['peak'] = memory_get_peak_usage(true);
        $results['health_score'] = $this->calculate_health_score($results['issues']);
        $results['performance_score'] = $this->calculate_performance_score($results['issues']);
        
        // Store results
        update_option('redco_diagnostic_results', $results);
        
        return $results;
    }
    
    /**
     * Get scan steps based on scan type
     */
    private function get_scan_steps($scan_type) {
        switch ($scan_type) {
            case 'comprehensive':
                return array('wordpress', 'database', 'frontend', 'server', 'security', 'modules');
            case 'quick':
                return array('wordpress', 'frontend');
            default:
                return array($scan_type);
        }
    }
    
    /**
     * Execute individual scan step
     */
    private function execute_scan_step($step) {
        switch ($step) {
            case 'wordpress':
                return $this->scan_wordpress_issues();
            case 'database':
                return $this->scan_database_issues();
            case 'frontend':
                return $this->scan_frontend_issues();
            case 'server':
                return $this->scan_server_issues();
            case 'security':
                return $this->scan_security_issues();
            case 'modules':
                return $this->scan_module_issues();
            default:
                return array();
        }
    }
    
    /**
     * Memory management methods
     */
    private function check_memory_usage() {
        $current_memory = memory_get_usage(true);
        $memory_threshold = $this->memory_limit_bytes * 0.8; // 80% threshold
        
        if ($current_memory > $memory_threshold) {
            $this->cleanup_memory();
            
            // If still over threshold, throw exception
            if (memory_get_usage(true) > $memory_threshold) {
                throw new Exception('Memory usage exceeded safe threshold');
            }
        }
    }
    
    /**
     * Force garbage collection and cleanup
     */
    private function cleanup_memory() {
        if (function_exists('gc_collect_cycles')) {
            gc_collect_cycles();
        }
        
        // Clear any large temporary variables
        global $wpdb;
        if (isset($wpdb->queries)) {
            $wpdb->queries = array();
        }
    }
    
    /**
     * Parse memory limit string to bytes
     */
    private function parse_memory_limit($memory_limit) {
        if (is_numeric($memory_limit)) {
            return (int) $memory_limit;
        }
        
        $unit = strtoupper(substr($memory_limit, -1));
        $value = (int) substr($memory_limit, 0, -1);
        
        switch ($unit) {
            case 'G':
                return $value * 1024 * 1024 * 1024;
            case 'M':
                return $value * 1024 * 1024;
            case 'K':
                return $value * 1024;
            default:
                return $value;
        }
    }
    
    /**
     * Update scan progress
     */
    private function update_scan_progress($current_step, $total_steps, $message) {
        $progress = array(
            'current_step' => $current_step,
            'total_steps' => $total_steps,
            'percentage' => round(($current_step / $total_steps) * 100),
            'message' => $message,
            'timestamp' => time()
        );
        
        set_transient('redco_scan_progress', $progress, 300);
        do_action('redco_scan_progress_update', $progress);
    }
    
    /**
     * Scan WordPress core issues
     */
    private function scan_wordpress_issues() {
        $issues = array();

        try {
            // Check WordPress version
            $wp_version = get_bloginfo('version');
            $latest_version = $this->get_latest_wp_version();

            if (version_compare($wp_version, $latest_version, '<')) {
                $issues[] = array(
                    'id' => 'outdated_wordpress',
                    'title' => 'WordPress Core Outdated',
                    'description' => "WordPress {$wp_version} is outdated. Latest version is {$latest_version}.",
                    'severity' => 'high',
                    'category' => 'security',
                    'auto_fixable' => false,
                    'fix_action' => 'update_wordpress',
                    'impact' => 'Security vulnerabilities and missing features'
                );
            }

            // Check for debug mode in production
            if (defined('WP_DEBUG') && WP_DEBUG && !$this->is_development_environment()) {
                $issues[] = array(
                    'id' => 'debug_mode_enabled',
                    'title' => 'Debug Mode Enabled in Production',
                    'description' => 'WP_DEBUG is enabled on a production site, which can expose sensitive information.',
                    'severity' => 'medium',
                    'category' => 'security',
                    'auto_fixable' => false,
                    'fix_action' => 'disable_debug_mode',
                    'impact' => 'Information disclosure risk'
                );
            }

            // Check for default admin user
            $admin_user = get_user_by('login', 'admin');
            if ($admin_user) {
                $issues[] = array(
                    'id' => 'default_admin_user',
                    'title' => 'Default Admin Username',
                    'description' => 'The default "admin" username is still active, making brute force attacks easier.',
                    'severity' => 'medium',
                    'category' => 'security',
                    'auto_fixable' => false,
                    'fix_action' => 'change_admin_username',
                    'impact' => 'Increased vulnerability to brute force attacks'
                );
            }

            // Check file permissions
            $permission_issues = $this->check_file_permissions();
            $issues = array_merge($issues, $permission_issues);

        } catch (Exception $e) {
            Redco_Diagnostic_Error_Handler::handle_exception($e, Redco_Diagnostic_Error_Handler::CONTEXT_SCAN);
        }

        return $issues;
    }

    /**
     * Scan database performance issues
     */
    private function scan_database_issues() {
        $issues = array();

        try {
            // Initialize database helper
            $database = new Redco_Diagnostic_Database();

            // Check autoload size
            $autoload_size = $database->get_autoload_size();
            if ($autoload_size > 1000000) { // 1MB threshold
                $issues[] = array(
                    'id' => 'large_autoload',
                    'title' => 'Large Autoload Options',
                    'description' => sprintf('Autoload options are %s, which can slow down every page load.', $this->format_bytes($autoload_size)),
                    'severity' => 'high',
                    'category' => 'performance',
                    'auto_fixable' => true,
                    'fix_action' => 'optimize_autoload',
                    'impact' => 'Slower page load times'
                );
            }

            // Check for large individual autoload options
            $large_options = $database->get_large_autoload_options(50000, 5);
            if (!empty($large_options)) {
                $issues[] = array(
                    'id' => 'large_autoload_options',
                    'title' => 'Individual Large Autoload Options',
                    'description' => sprintf('Found %d autoload options larger than 50KB each.', count($large_options)),
                    'severity' => 'medium',
                    'category' => 'performance',
                    'auto_fixable' => true,
                    'fix_action' => 'optimize_large_options',
                    'impact' => 'Memory usage and slower queries'
                );
            }

            // Check database size
            $db_size = $database->get_database_size();
            if ($db_size > 100 * 1024 * 1024) { // 100MB threshold
                $issues[] = array(
                    'id' => 'large_database',
                    'title' => 'Large Database Size',
                    'description' => sprintf('Database size is %s, consider optimization.', $this->format_bytes($db_size)),
                    'severity' => 'medium',
                    'category' => 'performance',
                    'auto_fixable' => true,
                    'fix_action' => 'optimize_database',
                    'impact' => 'Slower queries and increased resource usage'
                );
            }

            // Check for expired transients
            $expired_transients = $this->count_expired_transients();
            if ($expired_transients > 100) {
                $issues[] = array(
                    'id' => 'expired_transients',
                    'title' => 'Expired Transients',
                    'description' => sprintf('Found %d expired transients that should be cleaned up.', $expired_transients),
                    'severity' => 'low',
                    'category' => 'performance',
                    'auto_fixable' => true,
                    'fix_action' => 'cleanup_transients',
                    'impact' => 'Database bloat and slower queries'
                );
            }

        } catch (Exception $e) {
            Redco_Diagnostic_Error_Handler::handle_exception($e, Redco_Diagnostic_Error_Handler::CONTEXT_SCAN);
        }

        return $issues;
    }

    /**
     * Scan frontend performance issues
     */
    private function scan_frontend_issues() {
        $issues = array();

        try {
            // Check for GZIP compression
            if (!$this->is_compression_enabled()) {
                $issues[] = array(
                    'id' => 'gzip_compression_disabled',
                    'title' => 'GZIP Compression Disabled',
                    'description' => 'GZIP compression is not enabled, resulting in larger file transfers.',
                    'severity' => 'high',
                    'category' => 'performance',
                    'auto_fixable' => true,
                    'fix_action' => 'enable_gzip_compression',
                    'impact' => 'Slower page load times and increased bandwidth usage'
                );
            }

            // Check for browser caching
            if (!$this->has_cache_headers()) {
                $issues[] = array(
                    'id' => 'browser_caching_disabled',
                    'title' => 'Browser Caching Not Optimized',
                    'description' => 'Browser caching headers are not properly configured.',
                    'severity' => 'medium',
                    'category' => 'performance',
                    'auto_fixable' => true,
                    'fix_action' => 'enable_browser_caching',
                    'impact' => 'Repeat visitors experience slower load times'
                );
            }

            // Check for render-blocking resources
            $render_blocking = $this->check_render_blocking_resources();
            if ($render_blocking['count'] > 0) {
                $issues[] = array(
                    'id' => 'render_blocking_resources',
                    'title' => 'Render-Blocking Resources',
                    'description' => sprintf('Found %d render-blocking CSS/JS resources.', $render_blocking['count']),
                    'severity' => 'medium',
                    'category' => 'performance',
                    'auto_fixable' => false,
                    'fix_action' => 'optimize_render_blocking',
                    'impact' => 'Delayed page rendering and poor user experience'
                );
            }

            // Check image optimization
            $unoptimized_images = $this->check_unoptimized_images();
            if ($unoptimized_images > 0) {
                $issues[] = array(
                    'id' => 'unoptimized_images',
                    'title' => 'Unoptimized Images',
                    'description' => sprintf('Found %d images that could be optimized.', $unoptimized_images),
                    'severity' => 'medium',
                    'category' => 'performance',
                    'auto_fixable' => false,
                    'fix_action' => 'optimize_images',
                    'impact' => 'Larger page sizes and slower load times'
                );
            }

        } catch (Exception $e) {
            Redco_Diagnostic_Error_Handler::handle_exception($e, Redco_Diagnostic_Error_Handler::CONTEXT_SCAN);
        }

        return $issues;
    }

    /**
     * Scan server configuration issues
     */
    private function scan_server_issues() {
        $issues = array();

        try {
            // Check PHP version
            $php_version = PHP_VERSION;
            if (version_compare($php_version, '8.0', '<')) {
                $issues[] = array(
                    'id' => 'outdated_php',
                    'title' => 'Outdated PHP Version',
                    'description' => sprintf('PHP %s is outdated. Consider upgrading to PHP 8.0 or higher.', $php_version),
                    'severity' => 'high',
                    'category' => 'performance',
                    'auto_fixable' => false,
                    'fix_action' => 'update_php',
                    'impact' => 'Security vulnerabilities and performance issues'
                );
            }

            // Check memory limit
            $memory_limit = ini_get('memory_limit');
            $memory_bytes = $this->parse_memory_limit($memory_limit);
            if ($memory_bytes < 256 * 1024 * 1024) { // 256MB
                $issues[] = array(
                    'id' => 'low_memory_limit',
                    'title' => 'Low PHP Memory Limit',
                    'description' => sprintf('PHP memory limit is %s, which may be insufficient for complex operations.', $memory_limit),
                    'severity' => 'medium',
                    'category' => 'performance',
                    'auto_fixable' => false,
                    'fix_action' => 'increase_memory_limit',
                    'impact' => 'Potential memory exhaustion errors'
                );
            }

            // Check execution time limit
            $max_execution_time = ini_get('max_execution_time');
            if ($max_execution_time > 0 && $max_execution_time < 60) {
                $issues[] = array(
                    'id' => 'low_execution_time',
                    'title' => 'Low Execution Time Limit',
                    'description' => sprintf('Max execution time is %d seconds, which may cause timeouts.', $max_execution_time),
                    'severity' => 'medium',
                    'category' => 'performance',
                    'auto_fixable' => false,
                    'fix_action' => 'increase_execution_time',
                    'impact' => 'Script timeouts during complex operations'
                );
            }

            // Check for required PHP extensions
            $required_extensions = array('gd', 'curl', 'zip', 'mbstring');
            foreach ($required_extensions as $extension) {
                if (!extension_loaded($extension)) {
                    $issues[] = array(
                        'id' => 'missing_php_extension_' . $extension,
                        'title' => 'Missing PHP Extension: ' . strtoupper($extension),
                        'description' => sprintf('The %s PHP extension is not installed.', $extension),
                        'severity' => 'medium',
                        'category' => 'server',
                        'auto_fixable' => false,
                        'fix_action' => 'install_php_extension',
                        'impact' => 'Limited functionality for certain features'
                    );
                }
            }

        } catch (Exception $e) {
            Redco_Diagnostic_Error_Handler::handle_exception($e, Redco_Diagnostic_Error_Handler::CONTEXT_SCAN);
        }

        return $issues;
    }

    /**
     * Scan security configuration issues
     */
    private function scan_security_issues() {
        $issues = array();

        try {
            // Check for .htaccess BOM issues
            if (class_exists('Redco_Diagnostic_AutoFix_Engine')) {
                $engine = new Redco_Diagnostic_AutoFix_Engine();
                $bom_check = $engine->check_htaccess_bom_issues();

                if ($bom_check['has_issues']) {
                    $issues[] = array(
                        'id' => 'htaccess_bom_detected',
                        'title' => 'BOM Characters in .htaccess',
                        'description' => $bom_check['message'],
                        'severity' => 'critical',
                        'category' => 'security',
                        'auto_fixable' => true,
                        'fix_action' => 'clean_htaccess_bom',
                        'impact' => 'Website may return Internal Server Errors'
                    );
                }
            }

            // Check SSL configuration
            if (!is_ssl()) {
                $issues[] = array(
                    'id' => 'ssl_not_enabled',
                    'title' => 'SSL Not Enabled',
                    'description' => 'The website is not using HTTPS, which affects security and SEO.',
                    'severity' => 'high',
                    'category' => 'security',
                    'auto_fixable' => false,
                    'fix_action' => 'enable_ssl',
                    'impact' => 'Data transmission is not encrypted'
                );
            }

            // Check for security headers
            $security_headers = $this->check_security_headers();
            foreach ($security_headers as $header => $present) {
                if (!$present) {
                    $issues[] = array(
                        'id' => 'missing_security_header_' . strtolower(str_replace('-', '_', $header)),
                        'title' => 'Missing Security Header: ' . $header,
                        'description' => sprintf('The %s security header is not configured.', $header),
                        'severity' => 'medium',
                        'category' => 'security',
                        'auto_fixable' => true,
                        'fix_action' => 'add_security_headers',
                        'impact' => 'Reduced protection against various attacks'
                    );
                }
            }

            // Check file permissions
            $sensitive_files = array(
                ABSPATH . 'wp-config.php' => '644',
                ABSPATH . '.htaccess' => '644'
            );

            foreach ($sensitive_files as $file => $recommended_perm) {
                if (file_exists($file)) {
                    $current_perm = substr(sprintf('%o', fileperms($file)), -3);
                    if ($current_perm !== $recommended_perm && $current_perm !== '600') {
                        $issues[] = array(
                            'id' => 'incorrect_file_permissions_' . basename($file),
                            'title' => 'Incorrect File Permissions: ' . basename($file),
                            'description' => sprintf('File %s has permissions %s, recommended: %s', basename($file), $current_perm, $recommended_perm),
                            'severity' => 'medium',
                            'category' => 'security',
                            'auto_fixable' => false,
                            'fix_action' => 'fix_file_permissions',
                            'impact' => 'Potential unauthorized access to sensitive files'
                        );
                    }
                }
            }

        } catch (Exception $e) {
            Redco_Diagnostic_Error_Handler::handle_exception($e, Redco_Diagnostic_Error_Handler::CONTEXT_SCAN);
        }

        return $issues;
    }

    /**
     * Scan module-specific issues
     */
    private function scan_module_issues() {
        $issues = array();

        try {
            // Check if Redco modules are properly configured
            $modules = array('image-optimization', 'css-optimization', 'js-optimization', 'database-optimization');

            foreach ($modules as $module) {
                if (function_exists('redco_is_module_enabled') && redco_is_module_enabled($module)) {
                    // Check module-specific issues
                    $module_issues = $this->check_module_configuration($module);
                    $issues = array_merge($issues, $module_issues);
                }
            }

        } catch (Exception $e) {
            Redco_Diagnostic_Error_Handler::handle_exception($e, Redco_Diagnostic_Error_Handler::CONTEXT_SCAN);
        }

        return $issues;
    }

    /**
     * Scan PageSpeed issues (if API key is available)
     */
    private function scan_pagespeed_issues() {
        $issues = array();
        $score = 0;

        try {
            if (empty($this->settings['pagespeed_api_key'])) {
                return array('issues' => $issues, 'score' => $score);
            }

            $url = home_url();
            $api_key = $this->settings['pagespeed_api_key'];
            $api_url = "https://www.googleapis.com/pagespeed/v5/runPagespeed?url=" . urlencode($url) . "&key=" . $api_key;

            $response = wp_remote_get($api_url, array('timeout' => 30));

            if (!is_wp_error($response)) {
                $body = wp_remote_retrieve_body($response);
                $data = json_decode($body, true);

                if (isset($data['lighthouseResult']['categories']['performance']['score'])) {
                    $score = $data['lighthouseResult']['categories']['performance']['score'] * 100;

                    if ($score < 50) {
                        $issues[] = array(
                            'id' => 'low_pagespeed_score',
                            'title' => 'Low PageSpeed Score',
                            'description' => sprintf('PageSpeed Insights score is %d/100, which needs improvement.', $score),
                            'severity' => 'high',
                            'category' => 'performance',
                            'auto_fixable' => false,
                            'fix_action' => 'optimize_pagespeed',
                            'impact' => 'Poor user experience and SEO ranking'
                        );
                    }
                }
            }

        } catch (Exception $e) {
            Redco_Diagnostic_Error_Handler::handle_exception($e, Redco_Diagnostic_Error_Handler::CONTEXT_SCAN);
        }

        return array('issues' => $issues, 'score' => $score);
    }

    /**
     * Calculate health score based on issues
     */
    private function calculate_health_score($issues) {
        if (empty($issues)) {
            return 100;
        }

        $total_weight = 0;
        $severity_weights = array(
            'critical' => 25,
            'high' => 15,
            'medium' => 10,
            'low' => 5
        );

        foreach ($issues as $issue) {
            $severity = $issue['severity'] ?? 'low';
            $total_weight += $severity_weights[$severity] ?? 5;
        }

        $score = max(0, 100 - $total_weight);
        return round($score);
    }

    /**
     * Calculate performance score based on issues
     */
    private function calculate_performance_score($issues) {
        if (empty($issues)) {
            return 100;
        }

        $performance_issues = array_filter($issues, function($issue) {
            return ($issue['category'] ?? '') === 'performance';
        });

        if (empty($performance_issues)) {
            return 100;
        }

        $total_weight = 0;
        $severity_weights = array(
            'critical' => 30,
            'high' => 20,
            'medium' => 10,
            'low' => 5
        );

        foreach ($performance_issues as $issue) {
            $severity = $issue['severity'] ?? 'low';
            $total_weight += $severity_weights[$severity] ?? 5;
        }

        $score = max(0, 100 - $total_weight);
        return round($score);
    }

    /**
     * Helper methods for scanning
     */
    private function get_latest_wp_version() {
        $version_check = wp_remote_get('https://api.wordpress.org/core/version-check/1.7/');
        if (!is_wp_error($version_check)) {
            $version_data = json_decode(wp_remote_retrieve_body($version_check), true);
            if (isset($version_data['offers'][0]['version'])) {
                return $version_data['offers'][0]['version'];
            }
        }
        return get_bloginfo('version'); // Fallback to current version
    }

    private function is_development_environment() {
        return defined('WP_DEBUG') && WP_DEBUG &&
               (strpos(home_url(), 'localhost') !== false ||
                strpos(home_url(), '.local') !== false ||
                strpos(home_url(), 'staging') !== false);
    }

    private function check_file_permissions() {
        // Implementation for file permission checks
        return array();
    }

    private function format_bytes($size) {
        if (!function_exists('redco_format_bytes')) {
            $units = array('B', 'KB', 'MB', 'GB');
            for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
                $size /= 1024;
            }
            return round($size, 2) . ' ' . $units[$i];
        }
        return redco_format_bytes($size);
    }

    private function count_expired_transients() {
        global $wpdb;
        $current_time = time();
        $count = $wpdb->get_var($wpdb->prepare("
            SELECT COUNT(*)
            FROM {$wpdb->options}
            WHERE option_name LIKE '_transient_timeout_%'
            AND option_value < %d
        ", $current_time));

        return (int) $count;
    }

    private function is_compression_enabled() {
        // Check if GZIP compression is enabled
        return function_exists('gzencode') &&
               (isset($_SERVER['HTTP_ACCEPT_ENCODING']) &&
                strpos($_SERVER['HTTP_ACCEPT_ENCODING'], 'gzip') !== false);
    }

    private function has_cache_headers() {
        // Check if cache headers are properly configured
        $headers = headers_list();
        foreach ($headers as $header) {
            if (stripos($header, 'cache-control') !== false ||
                stripos($header, 'expires') !== false) {
                return true;
            }
        }
        return false;
    }

    private function check_render_blocking_resources() {
        // Simplified check - in real implementation, this would analyze the page
        return array('count' => 0);
    }

    private function check_unoptimized_images() {
        // Simplified check - in real implementation, this would scan media library
        return 0;
    }

    private function check_security_headers() {
        // CRITICAL FIX: Use comprehensive security header detection instead of hardcoded false values

        // Step 1: Check HTTP response headers
        $response = wp_remote_get(home_url(), array(
            'timeout' => 10,
            'headers' => array(
                'Cache-Control' => 'no-cache',
                'Pragma' => 'no-cache'
            )
        ));

        $http_headers = array();
        if (!is_wp_error($response)) {
            $http_headers = wp_remote_retrieve_headers($response);
        }

        // Step 2: Check .htaccess file for configured headers
        $htaccess_headers = $this->check_htaccess_security_headers();

        // Step 3: Combine results - header is present if found in either HTTP response or .htaccess
        $security_headers = array(
            'X-Content-Type-Options' => false,
            'X-Frame-Options' => false,
            'X-XSS-Protection' => false,
            'Strict-Transport-Security' => false
        );

        foreach ($security_headers as $header => $present) {
            $header_lower = strtolower($header);

            // Check HTTP response headers
            $in_http = isset($http_headers[$header_lower]) || isset($http_headers[$header]);

            // Check .htaccess configuration
            $in_htaccess = isset($htaccess_headers[$header]) && $htaccess_headers[$header];

            // Header is present if found in either location
            $security_headers[$header] = $in_http || $in_htaccess;
        }

        return $security_headers;
    }

    /**
     * Check .htaccess file for security headers configuration
     */
    private function check_htaccess_security_headers() {
        $htaccess_file = ABSPATH . '.htaccess';
        $headers_found = array(
            'X-Content-Type-Options' => false,
            'X-Frame-Options' => false,
            'X-XSS-Protection' => false,
            'Strict-Transport-Security' => false
        );

        if (!file_exists($htaccess_file)) {
            return $headers_found;
        }

        $content = file_get_contents($htaccess_file);
        if ($content === false) {
            return $headers_found;
        }

        // Check for each security header in .htaccess
        foreach ($headers_found as $header => $found) {
            if (strpos($content, $header) !== false) {
                $headers_found[$header] = true;
            }
        }

        return $headers_found;
    }

    private function check_module_configuration($module) {
        // Check module-specific configuration issues
        return array();
    }
}
