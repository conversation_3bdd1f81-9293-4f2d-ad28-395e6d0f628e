<?php
/**
 * Test fix history recording and Recent Fixes display
 */

// WordPress environment
define('WP_USE_THEMES', false);
require_once('../../../wp-load.php');

if (!current_user_can('manage_options')) {
    die('Access denied');
}

echo "<h1>🔧 Fix History Recording Test</h1>\n";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .test-section { border: 1px solid #ddd; margin: 20px 0; padding: 15px; }
    .success { color: green; }
    .error { color: red; }
    .warning { color: orange; }
    pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
</style>\n";

echo "<div class='test-section'>\n";
echo "<h2>Step 1: Check Current Fix History</h2>\n";

$fix_history = get_option('redco_diagnostic_fix_history', array());
echo "Current fix history count: " . count($fix_history) . "<br>\n";

if (empty($fix_history)) {
    echo "<div class='warning'>⚠️ No fix history found - creating test data</div>\n";
    
    // Create test fix history
    $test_sessions = array(
        array(
            'timestamp' => time() - 3600, // 1 hour ago
            'fixes_applied' => 3,
            'fixes_failed' => 0,
            'backup_created' => true,
            'rollback_id' => 'test_rollback_' . (time() - 3600),
            'details' => array(
                array(
                    'issue_id' => 'missing_security_header_x_frame_options',
                    'issue_title' => 'Missing Security Header: X-Frame-Options',
                    'fix_type' => 'security_header',
                    'status' => 'success',
                    'message' => 'Added X-Frame-Options header to .htaccess',
                    'success' => true
                ),
                array(
                    'issue_id' => 'missing_security_header_x_content_type_options',
                    'issue_title' => 'Missing Security Header: X-Content-Type-Options',
                    'fix_type' => 'security_header',
                    'status' => 'success',
                    'message' => 'Added X-Content-Type-Options header to .htaccess',
                    'success' => true
                ),
                array(
                    'issue_id' => 'no_compression',
                    'issue_title' => 'GZIP Compression Not Enabled',
                    'fix_type' => 'compression',
                    'status' => 'success',
                    'message' => 'Enabled GZIP compression in .htaccess',
                    'success' => true
                )
            )
        ),
        array(
            'timestamp' => time() - 1800, // 30 minutes ago
            'fixes_applied' => 1,
            'fixes_failed' => 1,
            'backup_created' => true,
            'rollback_id' => 'test_rollback_' . (time() - 1800),
            'details' => array(
                array(
                    'issue_id' => 'browser_caching_disabled',
                    'issue_title' => 'Browser Caching Not Configured',
                    'fix_type' => 'caching',
                    'status' => 'success',
                    'message' => 'Added browser caching headers to .htaccess',
                    'success' => true
                ),
                array(
                    'issue_id' => 'large_autoload',
                    'issue_title' => 'Large Database Autoload',
                    'fix_type' => 'database',
                    'status' => 'failed',
                    'message' => 'Failed to optimize autoload data - manual intervention required',
                    'success' => false
                )
            )
        ),
        array(
            'timestamp' => time() - 300, // 5 minutes ago
            'fixes_applied' => 2,
            'fixes_failed' => 0,
            'backup_created' => true,
            'rollback_id' => 'test_rollback_' . (time() - 300),
            'details' => array(
                array(
                    'issue_id' => 'missing_security_header_strict_transport_security',
                    'issue_title' => 'Missing Security Header: Strict-Transport-Security',
                    'fix_type' => 'security_header',
                    'status' => 'success',
                    'message' => 'Added Strict-Transport-Security header to .htaccess',
                    'success' => true
                ),
                array(
                    'issue_id' => 'wp_debug_enabled',
                    'issue_title' => 'WordPress Debug Mode Enabled',
                    'fix_type' => 'wordpress_config',
                    'status' => 'success',
                    'message' => 'Disabled WP_DEBUG in wp-config.php',
                    'success' => true
                )
            )
        )
    );
    
    update_option('redco_diagnostic_fix_history', $test_sessions);
    echo "<div class='success'>✅ Test fix history created with " . count($test_sessions) . " sessions</div>\n";
    $fix_history = $test_sessions;
} else {
    echo "<div class='success'>✅ Fix history exists</div>\n";
}

echo "</div>\n";

echo "<div class='test-section'>\n";
echo "<h2>Step 2: Test Recent Fixes AJAX Endpoint</h2>\n";

if (class_exists('Redco_Diagnostic_AutoFix')) {
    $diagnostic = new Redco_Diagnostic_AutoFix();
    
    // Simulate AJAX request
    $_POST['nonce'] = wp_create_nonce('redco_diagnostic_nonce');
    
    ob_start();
    try {
        $diagnostic->ajax_load_recent_fixes();
        $ajax_output = ob_get_clean();
        
        if (!empty($ajax_output)) {
            echo "<div class='success'>✅ AJAX endpoint responds</div>\n";
            
            // Parse JSON response
            $response = json_decode($ajax_output, true);
            if ($response && isset($response['success']) && $response['success']) {
                echo "<div class='success'>✅ AJAX response is valid JSON</div>\n";
                echo "<h4>Response Data:</h4>\n";
                echo "HTML length: " . strlen($response['data']['html']) . " characters<br>\n";
                echo "Fix count: " . $response['data']['count'] . "<br>\n";
                
                echo "<h4>Generated HTML:</h4>\n";
                echo "<div style='border: 1px solid #ccc; padding: 10px; background: #f9f9f9;'>\n";
                echo $response['data']['html'];
                echo "</div>\n";
            } else {
                echo "<div class='error'>❌ AJAX response is not valid JSON or failed</div>\n";
                echo "<pre>" . htmlspecialchars($ajax_output) . "</pre>\n";
            }
        } else {
            echo "<div class='error'>❌ AJAX endpoint returned no data</div>\n";
        }
    } catch (Exception $e) {
        ob_end_clean();
        echo "<div class='error'>❌ AJAX endpoint error: " . $e->getMessage() . "</div>\n";
    }
} else {
    echo "<div class='error'>❌ Redco_Diagnostic_AutoFix class not found</div>\n";
}

echo "</div>\n";

echo "<div class='test-section'>\n";
echo "<h2>Step 3: Test JavaScript Integration</h2>\n";

echo "<p>Testing if the Recent Fixes section loads properly in the interface...</p>\n";

echo "<div id='recent-fixes-container'>\n";
echo "    <div class='recent-fixes-loading'>\n";
echo "        <span class='dashicons dashicons-update'></span>\n";
echo "        Loading recent fixes...\n";
echo "    </div>\n";
echo "</div>\n";

echo "<script>
// Simulate the loadRecentFixes function
console.log('🔧 Testing Recent Fixes Loading...');

// Check if jQuery is available
if (typeof jQuery !== 'undefined') {
    console.log('✅ jQuery is available');
    
    // Simulate AJAX call
    jQuery.ajax({
        url: '" . admin_url('admin-ajax.php') . "',
        type: 'POST',
        data: {
            action: 'redco_load_recent_fixes',
            nonce: '" . wp_create_nonce('redco_diagnostic_nonce') . "'
        },
        success: function(response) {
            console.log('✅ Recent fixes AJAX success:', response);
            if (response.success) {
                jQuery('#recent-fixes-container').html(response.data.html);
                console.log('✅ Recent fixes HTML updated');
            } else {
                console.error('❌ Recent fixes AJAX failed:', response.data);
                jQuery('#recent-fixes-container').html('<div class=\"no-fixes-message\"><p>Failed to load recent fixes.</p></div>');
            }
        },
        error: function(xhr, status, error) {
            console.error('❌ Recent fixes AJAX error:', xhr, status, error);
            jQuery('#recent-fixes-container').html('<div class=\"no-fixes-message\"><p>Error loading recent fixes.</p></div>');
        }
    });
} else {
    console.error('❌ jQuery is not available');
    document.getElementById('recent-fixes-container').innerHTML = '<div class=\"no-fixes-message\"><p>jQuery not available for AJAX.</p></div>';
}
</script>\n";

echo "</div>\n";

echo "<h2>🎯 Test Summary</h2>\n";
echo "<p>This test verifies:</p>\n";
echo "<ul>\n";
echo "<li>✅ Fix history storage and retrieval</li>\n";
echo "<li>✅ Recent Fixes AJAX endpoint functionality</li>\n";
echo "<li>✅ HTML generation for Recent Fixes display</li>\n";
echo "<li>✅ JavaScript integration and AJAX calls</li>\n";
echo "</ul>\n";
echo "<p>Check the browser console for JavaScript debug information.</p>\n";
?>
