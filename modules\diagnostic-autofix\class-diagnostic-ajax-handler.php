<?php
/**
 * Standardized AJAX Handler for Diagnostic Module
 * 
 * Provides consistent AJAX handling with proper nonce verification,
 * error handling, and response formatting
 */

if (!defined('ABSPATH')) {
    exit;
}

class Redco_Diagnostic_Ajax_Handler {
    
    /**
     * Nonce action name (standardized)
     */
    const NONCE_ACTION = 'redco_diagnostic_nonce';
    
    /**
     * Instance
     */
    private static $instance = null;
    
    /**
     * Registered handlers
     */
    private $handlers = array();
    
    /**
     * Get singleton instance
     */
    public static function get_instance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->register_core_handlers();
    }
    
    /**
     * Register core AJAX handlers
     */
    private function register_core_handlers() {
        $this->register_handler('redco_run_diagnostic_scan', array($this, 'handle_diagnostic_scan'));
        $this->register_handler('redco_apply_auto_fixes', array($this, 'handle_auto_fixes'));
        $this->register_handler('redco_apply_single_fix', array($this, 'handle_single_fix'));
        $this->register_handler('redco_rollback_fixes', array($this, 'handle_rollback_fixes'));
        $this->register_handler('redco_get_recent_fixes', array($this, 'handle_get_recent_fixes'));
        $this->register_handler('redco_get_diagnostic_results', array($this, 'handle_get_diagnostic_results'));
        $this->register_handler('redco_get_performance_metrics', array($this, 'handle_get_performance_metrics'));
        $this->register_handler('redco_clean_htaccess_bom', array($this, 'handle_clean_htaccess_bom'));
        $this->register_handler('redco_debug_ajax_data', array($this, 'handle_debug_ajax_data'));
    }
    
    /**
     * Register AJAX handler with standardized security and error handling
     */
    public function register_handler($action, $callback, $require_auth = true) {
        $this->handlers[$action] = array(
            'callback' => $callback,
            'require_auth' => $require_auth
        );
        
        add_action("wp_ajax_{$action}", array($this, 'process_ajax_request'));
        
        if (!$require_auth) {
            add_action("wp_ajax_nopriv_{$action}", array($this, 'process_ajax_request'));
        }
    }
    
    /**
     * Process AJAX request with standardized security and error handling
     */
    public function process_ajax_request() {
        $action = $_POST['action'] ?? '';
        
        if (!isset($this->handlers[$action])) {
            Redco_Diagnostic_Error_Handler::handle_ajax_error(
                'INVALID_ACTION',
                'Invalid AJAX action',
                Redco_Diagnostic_Error_Handler::CONTEXT_AJAX,
                array('action' => $action)
            );
            return;
        }
        
        $handler = $this->handlers[$action];
        
        try {
            // Verify nonce
            if (!$this->verify_nonce()) {
                Redco_Diagnostic_Error_Handler::handle_ajax_error(
                    'NONCE_VERIFICATION_FAILED',
                    'Security verification failed. Please refresh the page and try again.',
                    Redco_Diagnostic_Error_Handler::CONTEXT_SECURITY,
                    array(
                        'action' => $action,
                        'nonce_received' => $_POST['nonce'] ?? 'none',
                        'user_id' => get_current_user_id()
                    )
                );
                return;
            }
            
            // Check user capabilities
            if ($handler['require_auth'] && !current_user_can('manage_options')) {
                Redco_Diagnostic_Error_Handler::handle_ajax_error(
                    'INSUFFICIENT_PERMISSIONS',
                    'You do not have permission to perform this action.',
                    Redco_Diagnostic_Error_Handler::CONTEXT_SECURITY,
                    array(
                        'action' => $action,
                        'user_id' => get_current_user_id(),
                        'required_capability' => 'manage_options'
                    )
                );
                return;
            }
            
            // Execute handler
            call_user_func($handler['callback']);
            
        } catch (Exception $e) {
            Redco_Diagnostic_Error_Handler::handle_exception(
                $e,
                Redco_Diagnostic_Error_Handler::CONTEXT_AJAX,
                array('action' => $action)
            );
            
            wp_send_json_error(array(
                'message' => 'An error occurred while processing your request.',
                'error_code' => 'HANDLER_EXCEPTION',
                'debug_info' => defined('WP_DEBUG') && WP_DEBUG ? $e->getMessage() : null
            ));
        }
    }
    
    /**
     * Verify nonce with standardized action
     */
    private function verify_nonce() {
        $nonce = $_POST['nonce'] ?? '';
        return wp_verify_nonce($nonce, self::NONCE_ACTION);
    }
    
    /**
     * Send standardized success response
     */
    public static function send_success($data = array(), $message = '') {
        $response = array(
            'success' => true,
            'timestamp' => time(),
        );
        
        if ($message) {
            $response['message'] = $message;
        }
        
        if (!empty($data)) {
            $response['data'] = $data;
        }
        
        wp_send_json_success($response);
    }
    
    /**
     * Send standardized error response
     */
    public static function send_error($error_code, $message, $data = array()) {
        Redco_Diagnostic_Error_Handler::handle_ajax_error($error_code, $message, Redco_Diagnostic_Error_Handler::CONTEXT_AJAX, $data);
    }
    
    /**
     * AJAX Handler: Run diagnostic scan
     */
    public function handle_diagnostic_scan() {
        $scan_type = sanitize_text_field($_POST['scan_type'] ?? 'comprehensive');
        $include_pagespeed = !empty($_POST['include_pagespeed']);
        
        try {
            // Get scanner instance
            $scanner = new Redco_Diagnostic_Scanner();
            $results = $scanner->run_scan($scan_type, $include_pagespeed);
            
            self::send_success($results, 'Diagnostic scan completed successfully');
            
        } catch (Exception $e) {
            self::send_error(
                'SCAN_FAILED',
                'Diagnostic scan failed: ' . $e->getMessage(),
                array('scan_type' => $scan_type)
            );
        }
    }
    
    /**
     * AJAX Handler: Apply auto fixes
     */
    public function handle_auto_fixes() {
        try {
            // Get auto-fix engine
            if (!class_exists('Redco_Diagnostic_AutoFix_Engine')) {
                self::send_error(
                    'ENGINE_NOT_AVAILABLE',
                    'Auto-fix engine is not available',
                    array('missing_class' => 'Redco_Diagnostic_AutoFix_Engine')
                );
                return;
            }
            
            $engine = new Redco_Diagnostic_AutoFix_Engine();
            
            // Get auto-fixable issues from last scan
            $last_scan = get_option('redco_diagnostic_results', array());
            $auto_fixable_issues = array();
            
            if (!empty($last_scan['issues'])) {
                foreach ($last_scan['issues'] as $issue) {
                    if (!empty($issue['auto_fixable'])) {
                        $auto_fixable_issues[] = $issue;
                    }
                }
            }
            
            if (empty($auto_fixable_issues)) {
                self::send_error(
                    'NO_FIXABLE_ISSUES',
                    'No auto-fixable issues found',
                    array('total_issues' => count($last_scan['issues'] ?? array()))
                );
                return;
            }
            
            // Apply fixes
            $results = $engine->apply_auto_fixes($auto_fixable_issues, true);
            
            self::send_success($results, sprintf(
                '%d fixes applied successfully',
                $results['fixes_applied']
            ));
            
        } catch (Exception $e) {
            self::send_error(
                'AUTO_FIX_FAILED',
                'Auto-fix operation failed: ' . $e->getMessage()
            );
        }
    }
    
    /**
     * AJAX Handler: Apply single fix
     */
    public function handle_single_fix() {
        // Try multiple data sources for backward compatibility
        $issue_data = $_POST['issue_data'] ?? $_POST['target_data'] ?? '';
        $issue_id = $_POST['issue_id'] ?? '';
        $opportunity_id = $_POST['opportunity_id'] ?? '';

        // If no direct issue data, try to construct from ID fields
        if (empty($issue_data) && (!empty($issue_id) || !empty($opportunity_id))) {
            $issue_data = $this->construct_issue_data_from_ids($issue_id, $opportunity_id);
        }

        if (empty($issue_data)) {
            self::send_error('MISSING_ISSUE_DATA', 'Issue data is required. Please provide issue_data, issue_id, or opportunity_id.');
            return;
        }

        try {
            // Handle both JSON string and array formats
            if (is_string($issue_data)) {
                $issue = json_decode(stripslashes($issue_data), true);
            } else {
                $issue = $issue_data;
            }

            if (!$issue || !is_array($issue)) {
                self::send_error('INVALID_ISSUE_DATA', 'Invalid issue data format. Expected JSON object or array.');
                return;
            }

            // Validate required fields
            if (empty($issue['id']) && empty($issue['fix_action'])) {
                self::send_error('INCOMPLETE_ISSUE_DATA', 'Issue data must contain either "id" or "fix_action" field.');
                return;
            }

            // Get auto-fix engine
            if (!class_exists('Redco_Diagnostic_AutoFix_Engine')) {
                self::send_error('ENGINE_NOT_AVAILABLE', 'Auto-fix engine is not available');
                return;
            }

            $engine = new Redco_Diagnostic_AutoFix_Engine();
            $result = $engine->apply_fix($issue);

            if ($result['success']) {
                self::send_success($result, 'Fix applied successfully');
            } else {
                self::send_error(
                    'FIX_FAILED',
                    $result['message'] ?? 'Fix failed',
                    $result
                );
            }

        } catch (Exception $e) {
            self::send_error(
                'SINGLE_FIX_FAILED',
                'Single fix operation failed: ' . $e->getMessage(),
                array(
                    'issue_data' => $issue_data,
                    'issue_id' => $issue_id,
                    'opportunity_id' => $opportunity_id
                )
            );
        }
    }

    /**
     * Construct issue data from scan results using IDs
     */
    private function construct_issue_data_from_ids($issue_id, $opportunity_id) {
        $scan_results = get_option('redco_diagnostic_results', array());

        if (empty($scan_results)) {
            return null;
        }

        // Look for issue by ID
        if (!empty($issue_id) && !empty($scan_results['issues'])) {
            foreach ($scan_results['issues'] as $issue) {
                if (isset($issue['id']) && $issue['id'] === $issue_id) {
                    return $issue;
                }
            }
        }

        // Look for opportunity by ID
        if (!empty($opportunity_id) && !empty($scan_results['optimization_opportunities'])) {
            foreach ($scan_results['optimization_opportunities'] as $category => $opportunities) {
                if (is_array($opportunities)) {
                    foreach ($opportunities as $opportunity) {
                        if (isset($opportunity['id']) && $opportunity['id'] === $opportunity_id) {
                            // Convert opportunity to issue format
                            return array(
                                'id' => $opportunity['id'],
                                'title' => $opportunity['title'] ?? 'Optimization Opportunity',
                                'description' => $opportunity['description'] ?? '',
                                'fix_action' => $opportunity['id'],
                                'auto_fixable' => $opportunity['fixable'] ?? false,
                                'type' => 'optimization_opportunity',
                                'category' => $category
                            );
                        }
                    }
                }
            }
        }

        return null;
    }
    
    /**
     * AJAX Handler: Clean .htaccess BOM
     */
    public function handle_clean_htaccess_bom() {
        try {
            if (!class_exists('Redco_Diagnostic_AutoFix_Engine')) {
                self::send_error('ENGINE_NOT_AVAILABLE', 'Auto-fix engine is not available');
                return;
            }
            
            $engine = new Redco_Diagnostic_AutoFix_Engine();
            $result = $engine->clean_htaccess_bom();
            
            if ($result['success']) {
                self::send_success($result, $result['message']);
            } else {
                self::send_error('BOM_CLEAN_FAILED', $result['message'], $result);
            }
            
        } catch (Exception $e) {
            self::send_error(
                'BOM_CLEAN_EXCEPTION',
                'BOM cleaning failed: ' . $e->getMessage()
            );
        }
    }
    
    /**
     * Placeholder handlers for other methods
     */
    public function handle_rollback_fixes() {
        self::send_error('NOT_IMPLEMENTED', 'Rollback functionality not yet implemented');
    }
    
    public function handle_get_recent_fixes() {
        try {
            $fix_history = get_option('redco_diagnostic_fix_history', array());
            $recent_fixes = array_slice(array_reverse($fix_history), 0, 5);

            $html = '';
            if (!empty($recent_fixes)) {
                foreach ($recent_fixes as $fix_session) {
                    $html .= '<div class="fix-item">';
                    $html .= '<div class="fix-header">';
                    $html .= '<div class="fix-date">' . human_time_diff($fix_session['timestamp']) . ' ' . __('ago', 'redco-optimizer') . '</div>';
                    $html .= '<div class="fix-count">' . sprintf(__('%d fixes', 'redco-optimizer'), $fix_session['fixes_applied']) . '</div>';
                    $html .= '</div>';

                    // Show backup info if available
                    $has_backup = isset($fix_session['backup_created']) && $fix_session['backup_created'];
                    $has_rollback_id = isset($fix_session['rollback_id']) && !empty($fix_session['rollback_id']);

                    if ($has_backup || $has_rollback_id) {
                        $html .= '<div class="fix-backup">';
                        $html .= '<span class="dashicons dashicons-backup"></span>';
                        $html .= '<span>' . __('Backup created', 'redco-optimizer') . '</span>';

                        if ($has_rollback_id) {
                            $html .= '<button type="button" class="button-link rollback-fix" data-backup-id="' . esc_attr($fix_session['rollback_id']) . '">';
                            $html .= '<span class="dashicons dashicons-undo"></span>';
                            $html .= __('Rollback', 'redco-optimizer');
                            $html .= '</button>';
                        }
                        $html .= '</div>';
                    }
                    $html .= '</div>';
                }
            } else {
                $html = '<div class="no-fixes-message"><p>' . __('No recent fixes found.', 'redco-optimizer') . '</p></div>';
            }

            self::send_success(array(
                'html' => $html,
                'count' => count($recent_fixes)
            ), 'Recent fixes retrieved successfully');

        } catch (Exception $e) {
            self::send_error(
                'RECENT_FIXES_FAILED',
                'Failed to retrieve recent fixes: ' . $e->getMessage()
            );
        }
    }
    
    public function handle_get_diagnostic_results() {
        $results = get_option('redco_diagnostic_results', array());
        self::send_success($results, 'Diagnostic results retrieved');
    }
    
    public function handle_get_performance_metrics() {
        self::send_error('NOT_IMPLEMENTED', 'Performance metrics functionality not yet implemented');
    }

    /**
     * AJAX Handler: Debug AJAX data (for troubleshooting)
     */
    public function handle_debug_ajax_data() {
        if (!defined('WP_DEBUG') || !WP_DEBUG) {
            self::send_error('DEBUG_DISABLED', 'Debug mode is not enabled');
            return;
        }

        $debug_data = array(
            'post_data' => $_POST,
            'get_data' => $_GET,
            'server_data' => array(
                'REQUEST_METHOD' => $_SERVER['REQUEST_METHOD'] ?? 'unknown',
                'CONTENT_TYPE' => $_SERVER['CONTENT_TYPE'] ?? 'unknown',
                'HTTP_USER_AGENT' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
            ),
            'scan_results_available' => !empty(get_option('redco_diagnostic_results')),
            'timestamp' => time()
        );

        self::send_success($debug_data, 'Debug data retrieved');
    }
}
