<?php
/**
 * Standardized Error Handler for Diagnostic Module
 * 
 * Provides consistent error handling, logging, and user feedback
 */

if (!defined('ABSPATH')) {
    exit;
}

class Redco_Diagnostic_Error_Handler {
    
    /**
     * Error levels
     */
    const LEVEL_DEBUG = 'debug';
    const LEVEL_INFO = 'info';
    const LEVEL_WARNING = 'warning';
    const LEVEL_ERROR = 'error';
    const LEVEL_CRITICAL = 'critical';
    
    /**
     * Error contexts
     */
    const CONTEXT_SCAN = 'scan';
    const CONTEXT_FIX = 'fix';
    const CONTEXT_AJAX = 'ajax';
    const CONTEXT_SECURITY = 'security';
    const CONTEXT_SYSTEM = 'system';
    
    /**
     * Instance
     */
    private static $instance = null;
    
    /**
     * Settings
     */
    private $settings = array();
    
    /**
     * Get singleton instance
     */
    public static function get_instance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->settings = array(
            'log_to_file' => defined('WP_DEBUG') && WP_DEBUG,
            'log_to_wp' => true,
            'send_to_user' => true,
            'include_trace' => defined('WP_DEBUG') && WP_DEBUG,
            'max_log_size' => 10 * 1024 * 1024, // 10MB
        );
    }
    
    /**
     * Handle error with standardized format
     */
    public static function handle_error($level, $message, $context = self::CONTEXT_SYSTEM, $data = array()) {
        $instance = self::get_instance();
        return $instance->process_error($level, $message, $context, $data);
    }
    
    /**
     * Handle AJAX error with standardized response
     */
    public static function handle_ajax_error($error_code, $message, $context = self::CONTEXT_AJAX, $data = array()) {
        $instance = self::get_instance();
        
        $error_data = array(
            'message' => $message,
            'error_code' => $error_code,
            'context' => $context,
            'timestamp' => time(),
            'user_id' => get_current_user_id(),
            'request_uri' => $_SERVER['REQUEST_URI'] ?? '',
        );
        
        // Add debug info if enabled
        if ($instance->settings['include_trace'] && !empty($data)) {
            $error_data['debug_info'] = $data;
        }
        
        // Log the error
        $instance->log_error(self::LEVEL_ERROR, $message, $context, $error_data);
        
        // Send standardized AJAX response
        wp_send_json_error($error_data);
    }
    
    /**
     * Handle exception with full context
     */
    public static function handle_exception(Exception $exception, $context = self::CONTEXT_SYSTEM, $additional_data = array()) {
        $instance = self::get_instance();
        
        $error_data = array_merge($additional_data, array(
            'exception_class' => get_class($exception),
            'exception_message' => $exception->getMessage(),
            'exception_file' => $exception->getFile(),
            'exception_line' => $exception->getLine(),
            'exception_trace' => $exception->getTraceAsString(),
        ));
        
        $level = ($exception instanceof Error) ? self::LEVEL_CRITICAL : self::LEVEL_ERROR;
        
        return $instance->process_error($level, $exception->getMessage(), $context, $error_data);
    }
    
    /**
     * Process error with logging and user feedback
     */
    private function process_error($level, $message, $context, $data = array()) {
        $error_id = $this->generate_error_id();
        
        $error_record = array(
            'id' => $error_id,
            'level' => $level,
            'message' => $message,
            'context' => $context,
            'data' => $data,
            'timestamp' => time(),
            'user_id' => get_current_user_id(),
            'ip_address' => $this->get_client_ip(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'request_uri' => $_SERVER['REQUEST_URI'] ?? '',
        );
        
        // Log the error
        $this->log_error($level, $message, $context, $error_record);
        
        // Store error for potential debugging
        $this->store_error_record($error_record);
        
        return $error_id;
    }
    
    /**
     * Log error to various destinations
     */
    private function log_error($level, $message, $context, $data) {
        $log_message = $this->format_log_message($level, $message, $context, $data);
        
        // Log to WordPress error log
        if ($this->settings['log_to_wp']) {
            error_log($log_message);
        }
        
        // Log to custom file if enabled
        if ($this->settings['log_to_file']) {
            $this->log_to_file($log_message, $context);
        }
        
        // Send to external logging service if configured
        $this->send_to_external_service($level, $message, $context, $data);
    }
    
    /**
     * Format log message
     */
    private function format_log_message($level, $message, $context, $data) {
        $timestamp = date('Y-m-d H:i:s');
        $user_id = get_current_user_id();
        
        $formatted = "[{$timestamp}] REDCO-DIAGNOSTIC [{$level}] [{$context}] {$message}";
        
        if ($user_id) {
            $formatted .= " (User: {$user_id})";
        }
        
        if (!empty($data['error_code'])) {
            $formatted .= " (Code: {$data['error_code']})";
        }
        
        return $formatted;
    }
    
    /**
     * Log to custom file
     */
    private function log_to_file($message, $context) {
        $log_dir = WP_CONTENT_DIR . '/redco-logs/';
        
        if (!file_exists($log_dir)) {
            wp_mkdir_p($log_dir);
            
            // Create .htaccess to protect logs
            $htaccess_content = "Order deny,allow\nDeny from all\n";
            file_put_contents($log_dir . '.htaccess', $htaccess_content);
        }
        
        $log_file = $log_dir . "diagnostic-{$context}-" . date('Y-m-d') . '.log';
        
        // Rotate log if too large
        if (file_exists($log_file) && filesize($log_file) > $this->settings['max_log_size']) {
            $backup_file = $log_file . '.' . time() . '.bak';
            rename($log_file, $backup_file);
        }
        
        file_put_contents($log_file, $message . "\n", FILE_APPEND | LOCK_EX);
    }
    
    /**
     * Store error record for debugging
     */
    private function store_error_record($error_record) {
        $stored_errors = get_option('redco_diagnostic_errors', array());
        
        // Keep only last 100 errors
        if (count($stored_errors) >= 100) {
            $stored_errors = array_slice($stored_errors, -99);
        }
        
        $stored_errors[] = $error_record;
        update_option('redco_diagnostic_errors', $stored_errors);
    }
    
    /**
     * Send to external logging service
     */
    private function send_to_external_service($level, $message, $context, $data) {
        // Placeholder for external logging integration
        // Could integrate with services like Sentry, LogRocket, etc.
        
        if (defined('REDCO_EXTERNAL_LOGGING_ENDPOINT') && REDCO_EXTERNAL_LOGGING_ENDPOINT) {
            $payload = array(
                'level' => $level,
                'message' => $message,
                'context' => $context,
                'data' => $data,
                'site_url' => home_url(),
                'plugin_version' => REDCO_OPTIMIZER_VERSION ?? 'unknown',
            );
            
            wp_remote_post(REDCO_EXTERNAL_LOGGING_ENDPOINT, array(
                'body' => json_encode($payload),
                'headers' => array('Content-Type' => 'application/json'),
                'timeout' => 5,
                'blocking' => false, // Don't block execution
            ));
        }
    }
    
    /**
     * Generate unique error ID
     */
    private function generate_error_id() {
        return 'redco_' . uniqid() . '_' . time();
    }
    
    /**
     * Get client IP address
     */
    private function get_client_ip() {
        $ip_keys = array('HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR');
        
        foreach ($ip_keys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    }
    
    /**
     * Get recent errors for debugging
     */
    public static function get_recent_errors($limit = 50, $context = null) {
        $stored_errors = get_option('redco_diagnostic_errors', array());
        
        if ($context) {
            $stored_errors = array_filter($stored_errors, function($error) use ($context) {
                return $error['context'] === $context;
            });
        }
        
        return array_slice(array_reverse($stored_errors), 0, $limit);
    }
    
    /**
     * Clear stored errors
     */
    public static function clear_errors() {
        delete_option('redco_diagnostic_errors');
    }
}
