<?php
/**
 * Comprehensive test for critical diagnostic issues
 * Issue 1: Scan stats showing "undefined%" values
 * Issue 2: Fix persistence failure
 */

// WordPress environment
define('WP_USE_THEMES', false);
require_once('../../../wp-load.php');

if (!current_user_can('manage_options')) {
    die('Access denied');
}

echo "<h1>🔧 Critical Diagnostic Issues Investigation</h1>\n";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .test-section { border: 1px solid #ddd; margin: 20px 0; padding: 15px; }
    .success { color: green; }
    .error { color: red; }
    .warning { color: orange; }
    .info { color: blue; }
    pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
    .test-container { border: 1px solid #ccc; padding: 15px; margin: 10px 0; background: #f9f9f9; }
</style>\n";

// =============================================================================
// ISSUE 1: SCAN STATS "undefined%" VALUES
// =============================================================================

echo "<div class='test-section'>\n";
echo "<h2>🔍 ISSUE 1: Scan Stats 'undefined%' Values Investigation</h2>\n";

echo "<h3>Step 1: Check Current Scan Results Data Structure</h3>\n";
$scan_results = get_option('redco_diagnostic_results', array());

if (!empty($scan_results)) {
    echo "<div class='success'>✅ Scan results exist</div>\n";
    echo "<h4>Scan Results Structure:</h4>\n";
    echo "<pre>\n";
    echo "Keys: " . implode(', ', array_keys($scan_results)) . "\n";
    
    // Check for score fields
    $score_fields = ['health_score', 'performance_score', 'issues_found', 'critical_issues', 'auto_fixable'];
    foreach ($score_fields as $field) {
        $value = isset($scan_results[$field]) ? $scan_results[$field] : 'NOT SET';
        $type = isset($scan_results[$field]) ? gettype($scan_results[$field]) : 'undefined';
        echo "{$field}: {$value} (type: {$type})\n";
    }
    echo "</pre>\n";
    
    // Test percentage calculations
    echo "<h4>Testing Percentage Calculations:</h4>\n";
    if (isset($scan_results['health_score'])) {
        $health_score = $scan_results['health_score'];
        echo "Health Score: {$health_score}% (raw: {$health_score})<br>\n";
        
        // Test JavaScript-style concatenation
        $js_test = $health_score . '%';
        echo "JavaScript-style: {$js_test}<br>\n";
        
        // Test if it's actually undefined
        if ($health_score === null || $health_score === '') {
            echo "<div class='error'>❌ Health score is null/empty - this causes 'undefined%'</div>\n";
        }
    } else {
        echo "<div class='error'>❌ health_score field missing - this causes 'undefined%'</div>\n";
    }
} else {
    echo "<div class='warning'>⚠️ No scan results found</div>\n";
    
    // Run a test scan to generate data
    echo "<h4>Running Test Scan to Generate Data:</h4>\n";
    if (class_exists('Redco_Diagnostic_AutoFix')) {
        $diagnostic = new Redco_Diagnostic_AutoFix();
        
        // Simulate scan request
        $_POST['nonce'] = wp_create_nonce('redco_diagnostic_nonce');
        $_POST['scan_type'] = 'quick';
        
        ob_start();
        try {
            $diagnostic->ajax_run_diagnostic_scan();
            $scan_output = ob_get_clean();
            
            $response = json_decode($scan_output, true);
            if ($response && isset($response['success']) && $response['success']) {
                echo "<div class='success'>✅ Test scan completed</div>\n";
                
                // Check the generated data
                $new_scan_results = get_option('redco_diagnostic_results', array());
                if (!empty($new_scan_results)) {
                    echo "<h4>Generated Scan Data:</h4>\n";
                    echo "<pre>\n";
                    foreach ($score_fields as $field) {
                        $value = isset($new_scan_results[$field]) ? $new_scan_results[$field] : 'NOT SET';
                        $type = isset($new_scan_results[$field]) ? gettype($new_scan_results[$field]) : 'undefined';
                        echo "{$field}: {$value} (type: {$type})\n";
                    }
                    echo "</pre>\n";
                }
            } else {
                echo "<div class='error'>❌ Test scan failed</div>\n";
                echo "<pre>" . htmlspecialchars($scan_output) . "</pre>\n";
            }
        } catch (Exception $e) {
            ob_end_clean();
            echo "<div class='error'>❌ Test scan exception: " . $e->getMessage() . "</div>\n";
        }
    }
}

echo "</div>\n";

// =============================================================================
// ISSUE 2: FIX PERSISTENCE FAILURE
// =============================================================================

echo "<div class='test-section'>\n";
echo "<h2>🔍 ISSUE 2: Fix Persistence Failure Investigation</h2>\n";

echo "<h3>Step 1: Test Security Header Fix Persistence</h3>\n";

// Check current .htaccess content
$htaccess_file = ABSPATH . '.htaccess';
if (file_exists($htaccess_file)) {
    $htaccess_content = file_get_contents($htaccess_file);
    echo "<div class='info'>📄 .htaccess file exists (" . strlen($htaccess_content) . " bytes)</div>\n";
    
    // Check for security headers
    $security_headers = [
        'X-Frame-Options' => 'X-Frame-Options',
        'X-Content-Type-Options' => 'X-Content-Type-Options',
        'Strict-Transport-Security' => 'Strict-Transport-Security',
        'X-XSS-Protection' => 'X-XSS-Protection'
    ];
    
    echo "<h4>Current Security Headers in .htaccess:</h4>\n";
    foreach ($security_headers as $header => $search_term) {
        $found = strpos($htaccess_content, $search_term) !== false;
        $status = $found ? '✅ PRESENT' : '❌ MISSING';
        echo "{$header}: {$status}<br>\n";
    }
    
    // Check for GZIP compression
    $gzip_present = strpos($htaccess_content, 'mod_deflate') !== false || strpos($htaccess_content, 'mod_gzip') !== false;
    echo "GZIP Compression: " . ($gzip_present ? '✅ PRESENT' : '❌ MISSING') . "<br>\n";
    
    // Check for browser caching
    $caching_present = strpos($htaccess_content, 'mod_expires') !== false || strpos($htaccess_content, 'ExpiresActive') !== false;
    echo "Browser Caching: " . ($caching_present ? '✅ PRESENT' : '❌ MISSING') . "<br>\n";
    
} else {
    echo "<div class='error'>❌ .htaccess file does not exist</div>\n";
}

echo "<h3>Step 2: Test Fix Application and Detection</h3>\n";

// Test applying a security header fix
if (class_exists('Redco_Diagnostic_AutoFix_Engine')) {
    $engine = new Redco_Diagnostic_AutoFix_Engine();
    
    // Create a test issue for security header
    $test_issue = array(
        'id' => 'missing_security_header_x_frame_options',
        'title' => 'Missing Security Header: X-Frame-Options',
        'description' => 'The X-Frame-Options header is not configured',
        'category' => 'security',
        'severity' => 'medium',
        'fix_action' => 'add_security_header',
        'auto_fixable' => true,
        'header_name' => 'X-Frame-Options',
        'header_value' => 'SAMEORIGIN'
    );
    
    echo "<h4>Testing Security Header Fix:</h4>\n";
    echo "Issue: " . $test_issue['title'] . "<br>\n";
    
    // Check if issue is currently present
    if (class_exists('Redco_Diagnostic_AutoFix')) {
        $diagnostic = new Redco_Diagnostic_AutoFix();
        
        // Use reflection to access private method
        $reflection = new ReflectionClass($diagnostic);
        $method = $reflection->getMethod('check_security_issue');
        $method->setAccessible(true);
        
        $is_present_before = $method->invoke($diagnostic, $test_issue['id'], $test_issue);
        echo "Issue present before fix: " . ($is_present_before ? '❌ YES' : '✅ NO') . "<br>\n";
        
        // Apply the fix
        echo "<h4>Applying Fix:</h4>\n";
        $fix_result = $engine->apply_fix($test_issue);
        
        echo "Fix success: " . ($fix_result['success'] ? '✅ YES' : '❌ NO') . "<br>\n";
        echo "Fix message: " . $fix_result['message'] . "<br>\n";
        
        if (!empty($fix_result['changes_made'])) {
            echo "Changes made:<br>\n";
            foreach ($fix_result['changes_made'] as $change) {
                echo "- {$change}<br>\n";
            }
        }
        
        // Check if issue is still present after fix
        $is_present_after = $method->invoke($diagnostic, $test_issue['id'], $test_issue);
        echo "Issue present after fix: " . ($is_present_after ? '❌ YES (FIX FAILED)' : '✅ NO (FIX WORKED)') . "<br>\n";
        
        // Check .htaccess again
        if (file_exists($htaccess_file)) {
            $new_htaccess_content = file_get_contents($htaccess_file);
            $header_added = strpos($new_htaccess_content, 'X-Frame-Options') !== false;
            echo "X-Frame-Options in .htaccess after fix: " . ($header_added ? '✅ PRESENT' : '❌ MISSING') . "<br>\n";
            
            if ($header_added && $is_present_after) {
                echo "<div class='error'>🚨 CRITICAL: Header is in .htaccess but detection logic still reports issue as present!</div>\n";
                echo "<div class='error'>This indicates a problem with the issue detection logic.</div>\n";
            }
        }
    }
} else {
    echo "<div class='error'>❌ AutoFix Engine not available</div>\n";
}

echo "<h3>Step 3: Test Scan After Fix</h3>\n";

// Run another scan to see if the fix persists
if (class_exists('Redco_Diagnostic_AutoFix')) {
    $diagnostic = new Redco_Diagnostic_AutoFix();
    
    $_POST['nonce'] = wp_create_nonce('redco_diagnostic_nonce');
    $_POST['scan_type'] = 'quick';
    
    ob_start();
    try {
        $diagnostic->ajax_run_diagnostic_scan();
        $scan_output = ob_get_clean();
        
        $response = json_decode($scan_output, true);
        if ($response && isset($response['success']) && $response['success']) {
            echo "<div class='success'>✅ Post-fix scan completed</div>\n";
            
            // Check if the security header issue is still reported
            $post_fix_results = get_option('redco_diagnostic_results', array());
            if (!empty($post_fix_results['issues'])) {
                $security_issues = array_filter($post_fix_results['issues'], function($issue) {
                    return strpos($issue['id'], 'security_header') !== false;
                });
                
                if (!empty($security_issues)) {
                    echo "<div class='error'>❌ Security header issues still reported after fix:</div>\n";
                    foreach ($security_issues as $issue) {
                        echo "- " . $issue['title'] . "<br>\n";
                    }
                    echo "<div class='error'>🚨 This confirms the fix persistence failure!</div>\n";
                } else {
                    echo "<div class='success'>✅ No security header issues reported - fix persisted correctly</div>\n";
                }
            } else {
                echo "<div class='success'>✅ No issues found in post-fix scan</div>\n";
            }
        } else {
            echo "<div class='error'>❌ Post-fix scan failed</div>\n";
        }
    } catch (Exception $e) {
        ob_end_clean();
        echo "<div class='error'>❌ Post-fix scan exception: " . $e->getMessage() . "</div>\n";
    }
}

echo "</div>\n";

echo "<h2>🎯 Investigation Summary</h2>\n";
echo "<p>This test investigates both critical issues:</p>\n";
echo "<ul>\n";
echo "<li><strong>Issue 1:</strong> Checks if scan result fields are properly set and not null/undefined</li>\n";
echo "<li><strong>Issue 2:</strong> Tests fix application, file modification, and persistence detection</li>\n";
echo "</ul>\n";
echo "<p>Check the results above to identify the root causes of both issues.</p>\n";
?>
